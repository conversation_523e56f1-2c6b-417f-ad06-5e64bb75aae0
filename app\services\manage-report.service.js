/* models */
const Report = require('../models/report.model');
const Scope = require('../models/scope.model');
const UserReport = require('../models/user-report.model');

// utils
const { toObjectId } = require('../utils/common.utils');
const commonfunctionsUtils = require('../utils/common-function.utils');

// services
const scopeService = require('./scope.service');
const userReportService = require('./user-report.service');
const locationService = require('./location.service');
const assetService = require('./asset.service');

/**
 * Create Report
 *
 * @param {*} requestData
 * @returns
 */
exports.createReport = async (requestData, session) => {
  const report = new Report(requestData);
  return report.save({ session });
};

exports.getReports = async (filter, page, perPage, sort) => {
  return Report.find(filter, this.removeSelectFields())
    .sort({ sortOrder: sort ?? 1 })
    .limit(perPage)
    .skip(page * perPage)
    .populate(this.populateFields());
};

exports.updateReportById = async (id, requestData) =>
  Report.findByIdAndUpdate(id, { $set: requestData }, { new: true });

exports.getSingleReportByFilter = async filter => {
  return Report.findOne(filter, this.removeSelectFields()).populate(this.populateFields());
};

exports.deleteReport = async (id, requestData) => {
  return Report.findByIdAndUpdate(id, { $set: requestData }, { new: true });
};

exports.removeSelectFields = () => {
  return {
    updatedBy: 0,
    updatedAt: 0,
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  };
};

exports.populateFields = () => {
  return [
    {
      path: 'project',
      select: { _id: 1, title: 1, projectNumber: 1 },
      strictPopulate: false,
    },
    {
      path: 'account',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'createdBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ];
};

/**
 * Get One Report Id
 *
 * @param {*} filter
 * @returns
 */
exports.getOneReportId = async filter => {
  let agregateFunction = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1, projectNumber: 1 } }],
        as: 'project',
      },
    },
    {
      $unwind: {
        path: '$project',
      },
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        pipeline: [{ $project: { name: 1 } }],
        as: 'account',
      },
    },
    {
      $unwind: {
        path: '$account',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [{ $project: { callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'createdBy',
      },
    },
    {
      $unwind: {
        path: '$createdBy',
      },
    },
    {
      $lookup: {
        from: 'locations',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, title: 1 } }],
        as: 'locations',
      },
    },
    {
      $lookup: {
        from: 'assets',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, cableName: 1, manufacturer: 1, typeMm2: 1 } }],
        as: 'assets',
      },
    },
    {
      $lookup: {
        from: 'scopes',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, name: 1 } }],
        as: 'scope',
      },
    },
    {
      $unwind: {
        path: '$scopes',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        _id: 1,
        title: 1,
        project: 1,
        type: 1,
        status: 1,
        isProgressable: 1,
        isPublish: 1,
        sortOrder: 1,
        locations: 1,
        assets: 1,
        scope: 1,
        account: 1,
        createdBy: 1,
        createdAt: 1,
      },
    },
  ];

  return await Report.aggregate(agregateFunction);
};

/**
 * Get Report IDs
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 */
exports.getReportsIDs = async (filter, page = null, perPage = null, sort = null) => {
  let agregateFunction = [
    {
      $match: filter,
    },
    {
      $sort: { createdAt: sort ?? -1 },
    },
  ];

  if (page && perPage) {
    agregateFunction.push({
      $skip: Number.parseInt(page) * Number.parseInt(perPage),
    });
    agregateFunction.push({
      $limit: Number.parseInt(perPage),
    });
  }
  agregateFunction.push({
    $project: {
      _id: 1,
      title: 1,
      project: 1,
      type: 1,
      status: 1,
      isProgressable: 1,
      isPublish: 1,
      sortOrder: 1,
      locations: 1,
      assets: 1,
      scope: 1,
      account: 1,
      createdBy: 1,
      createdAt: 1,
    },
  });
  return await Report.aggregate(agregateFunction);
};

/**
 * Get Report Details
 *
 * @param {*} filter
 */
exports.getReportDetails = async filter => {
  let agreegateFunction = [
    {
      $match: { _id: filter },
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1, projectNumber: 1 } }],
        as: 'project',
      },
    },
    {
      $unwind: {
        path: '$project',
      },
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        pipeline: [{ $project: { name: 1 } }],
        as: 'account',
      },
    },
    {
      $unwind: {
        path: '$account',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [{ $project: { callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'createdBy',
      },
    },
    {
      $unwind: {
        path: '$createdBy',
      },
    },
    {
      $lookup: {
        from: 'locations',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, title: 1 } }],
        as: 'locations',
      },
    },
    {
      $lookup: {
        from: 'assets',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, cableName: 1 } }],
        as: 'assets',
      },
    },
    {
      $lookup: {
        from: 'scopes',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, name: 1 } }],
        as: 'scope',
      },
    },
    {
      $unwind: {
        path: '$scopes',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        _id: 1,
        title: 1,
        project: 1,
        type: 1,
        status: 1,
        isProgressable: 1,
        isPublish: 1,
        sortOrder: 1,
        locations: 1,
        assets: 1,
        scope: 1,
        account: 1,
        createdBy: 1,
        createdAt: 1,
      },
    },
  ];
  return await Report.aggregate(agreegateFunction);
};

/**
 * Get Report Details with Questions
 *
 * @param {*} filter
 */
exports.getReportDetailsWithQuestions = async filter => {
  let agreegateFunction = [
    {
      $match: { _id: filter },
    },
    {
      $lookup: {
        from: 'report-questions',
        localField: '_id',
        foreignField: 'report',
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
          { $sort: { sortOrder: 1 } },
          {
            $project: {
              title: 1,
              sortOrder: 1,
              duration: 1,
              isRequired: 1,
              weight: 1,
              supportedContent: 1,
              answerTypes: 1,
              createdBy: 1,
              createdAt: 1,
            },
          },
          {
            $lookup: {
              from: 'report-question-answers',
              localField: '_id',
              foreignField: 'reportQuestion',
              pipeline: [
                {
                  $match: {
                    deletedAt: null,
                  },
                },
                { $sort: { sortOrder: 1 } },
                {
                  $project: {
                    _id: 1,
                    title: {
                      $filter: {
                        input: '$title',
                        as: 'titleObj',
                        cond: { $eq: ['$$titleObj.isActive', true] },
                      },
                    },
                    parameterType: 1,
                    option: 1,
                    range: 1,
                    sortOrder: 1,
                    numberOfAnswers: 1,
                  },
                },
                {
                  $lookup: {
                    from: 'parameter-types',
                    localField: 'parameterType',
                    foreignField: '_id',
                    pipeline: [{ $project: { name: 1, uniqueKey: 1, isActive: 1 } }],
                    as: 'parameterType',
                  },
                },
                {
                  $unwind: {
                    path: '$parameterType',
                  },
                },
              ],
              as: 'answerTypes',
            },
          },
        ],
        as: 'reportQuestions',
      },
    },
    {
      $project: {
        reportQuestions: 1,
      },
    },
  ];
  return await Report.aggregate(agreegateFunction);
};

exports.getReportWithQuestion = async (
  filter,
  page = null,
  perPage = null,
  sort = null,
  questions = true
) => {
  let agreegateFunction = [
    {
      $match: filter,
    },
    {
      $sort: { createdAt: sort ?? -1 },
    },
  ];

  if (page && perPage) {
    agreegateFunction.push({
      $skip: Number.parseInt(page) * Number.parseInt(perPage),
    });
    agreegateFunction.push({
      $limit: Number.parseInt(perPage),
    });
  }

  agreegateFunction.push(
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1, projectNumber: 1 } }],
        as: 'project',
      },
    },
    {
      $unwind: {
        path: '$project',
      },
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        pipeline: [{ $project: { name: 1 } }],
        as: 'account',
      },
    },
    {
      $unwind: {
        path: '$account',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [{ $project: { callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'createdBy',
      },
    },
    {
      $unwind: {
        path: '$createdBy',
      },
    },
    {
      $lookup: {
        from: 'locations',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, title: 1 } }],
        as: 'locations',
      },
    },
    {
      $lookup: {
        from: 'assets',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, cableName: 1 } }],
        as: 'assets',
      },
    },
    {
      $lookup: {
        from: 'scopes',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, name: 1 } }],
        as: 'scope',
      },
    },
    {
      $unwind: {
        path: '$scopes',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'report-questions',
        localField: '_id',
        foreignField: 'report',
        pipeline: [{ $match: { deletedAt: null, duration: { $gt: 0 } } }],
        as: 'reportQue',
      },
    },
    {
      $addFields: {
        durationQuestions: { $sum: '$reportQue.duration' },
      },
    }
  );

  if (questions) {
    agreegateFunction.push({
      $lookup: {
        from: 'report-questions',
        localField: '_id',
        foreignField: 'report',
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
          { $sort: { sortOrder: 1 } },
          {
            $project: {
              title: 1,
              sortOrder: 1,
              duration: 1,
              isRequired: 1,
              weight: 1,
              supportedContent: 1,
              answerTypes: 1,
              createdBy: 1,
              createdAt: 1,
            },
          },
          {
            $lookup: {
              from: 'report-question-answers',
              localField: '_id',
              foreignField: 'reportQuestion',
              pipeline: [
                {
                  $match: {
                    deletedAt: null,
                  },
                },
                { $sort: { createdAt: 1 } },
                {
                  $project: {
                    _id: 1,
                    title: {
                      $filter: {
                        input: '$title',
                        as: 'titleObj',
                        cond: { $eq: ['$$titleObj.isActive', true] },
                      },
                    },
                    parameterType: 1,
                    option: 1,
                    range: 1,
                    numberOfAnswers: 1,
                  },
                },
                {
                  $lookup: {
                    from: 'parameter-types',
                    localField: 'parameterType',
                    foreignField: '_id',
                    pipeline: [{ $project: { name: 1, uniqueKey: 1, isActive: 1 } }],
                    as: 'parameterType',
                  },
                },
                {
                  $unwind: {
                    path: '$parameterType',
                  },
                },
              ],
              as: 'answerTypes',
            },
          },
        ],
        as: 'reportQuestions',
      },
    });
  }

  agreegateFunction.push({
    $project: {
      _id: 1,
      title: 1,
      project: 1,
      type: 1,
      status: 1,
      isProgressable: 1,
      isPublish: 1,
      sortOrder: 1,
      locations: 1,
      assets: 1,
      scope: 1,
      questionDuration: '$durationQuestions',
      reportQuestions: 1,
      account: 1,
      createdBy: 1,
      createdAt: 1,
    },
  });
  return await Report.aggregate(agreegateFunction);
};

exports.reportCalculation = async filter => {
  const scopeCal = await Scope.aggregate([
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'reports',
        localField: 'reports',
        foreignField: '_id',
        pipeline: [
          { $match: { deletedAt: null } },
          { $project: { _id: 1, title: 1, type: 1 } },
          {
            $lookup: {
              from: 'locations',
              localField: '_id',
              foreignField: 'reports',
              pipeline: [
                { $match: { deletedAt: null } },
                {
                  $group: { _id: null, total: { $sum: 1 } },
                },
              ],
              as: 'totalLocation',
            },
          },
          {
            $unwind: { path: '$totalLocation', preserveNullAndEmptyArrays: true },
          },
          {
            $lookup: {
              from: 'assets',
              localField: '_id',
              foreignField: 'reports',
              pipeline: [
                { $match: { deletedAt: null } },
                {
                  $addFields: {
                    fromLocationArray: {
                      $cond: {
                        if: { $isArray: '$fromLocation' },
                        then: '$fromLocation',
                        else: ['$fromLocation'],
                      },
                    },
                    toLocationArray: {
                      $cond: {
                        if: { $isArray: '$toLocation' },
                        then: '$toLocation',
                        else: ['$toLocation'],
                      },
                    },
                  },
                },
                {
                  $addFields: {
                    uniqueLocations: {
                      $setUnion: ['$fromLocationArray', '$toLocationArray'],
                    },
                  },
                },
                {
                  $unwind: '$uniqueLocations',
                },
                {
                  $group: {
                    _id: '$uniqueLocations',
                    total: { $sum: 1 },
                  },
                },
                {
                  $group: {
                    _id: null,
                    totalUniqueLocations: { $sum: 1 },
                  },
                },
              ],
              as: 'totalAsset',
            },
          },
          {
            $unwind: { path: '$totalAsset', preserveNullAndEmptyArrays: true },
          },
          {
            $lookup: {
              from: 'locations',
              localField: '_id',
              foreignField: 'reports',
              pipeline: [
                {
                  $match: { deletedAt: null },
                },
                { $project: { _id: 1, title: 1 } },
              ],
              as: 'locations',
            },
          },
          {
            $lookup: {
              from: 'assets',
              localField: '_id',
              foreignField: 'reports',
              pipeline: [
                {
                  $match: { deletedAt: null },
                },
                { $project: { _id: 1, cableName: 1, fromLocation: 1, toLocation: 1 } },
              ],
              as: 'assets',
            },
          },
          {
            $lookup: {
              from: 'report-questions',
              localField: '_id',
              foreignField: 'report',
              pipeline: [
                { $match: { deletedAt: null } },
                {
                  $group: { _id: null, total: { $sum: '$duration' } },
                },
              ],
              as: 'durationReport',
            },
          },
          {
            $unwind: '$durationReport',
          },
          {
            $addFields: {
              totalReportRequired: {
                $cond: {
                  if: { $eq: ['$type', 'location'] },
                  then: '$totalLocation.total',
                  else: {
                    $cond: {
                      if: {
                        $or: [
                          { $eq: ['$type', 'asset_per_location'] },
                          { $eq: ['$type', 'multiple_assets'] },
                        ],
                      },
                      then: '$totalAsset.totalUniqueLocations',
                      else: 0,
                    },
                  },
                },
              },
              durationReport: '$durationReport.total',
            },
          },
          {
            $addFields: {
              totalDurationReport: {
                $multiply: ['$totalReportRequired', '$durationReport'],
              },
            },
          },
        ],
        as: 'reports',
      },
    },
    {
      $unwind: {
        path: '$reports',
      },
    },
    {
      //group data for scope
      $group: {
        _id: { _id: '$_id', name: '$name' },
        sortOrder: { $first: '$sortOrder' },
        reports: { $push: '$reports' },
        totalDurationOfAllScopes: { $sum: '$reports.totalDurationReport' },
      },
    },
    {
      //group data for all report
      $group: {
        _id: null,
        scopeData: {
          $addToSet: {
            _id: '$_id._id',
            name: '$_id.name',
            sortOrder: '$sortOrder',
            reports: '$reports',
            totalDurationOfAllScopes: '$totalDurationOfAllScopes',
          },
        },
        totalDurationOfAllReports: { $sum: '$totalDurationOfAllScopes' },
      },
    },
    {
      $addFields: {
        scopeData: {
          $map: {
            input: '$scopeData',
            as: 'scope',
            in: {
              $mergeObjects: [
                '$$scope',
                {
                  reports: {
                    $map: {
                      input: '$$scope.reports',
                      as: 'report',
                      in: {
                        $mergeObjects: [
                          '$$report',
                          {
                            // report progress on each location or asset
                            reportProgress: {
                              $cond: {
                                if: {
                                  $and: [
                                    { $gt: ['$$report.totalDurationReport', 0] },
                                    { $gt: ['$totalDurationOfAllReports', 0] },
                                  ],
                                },
                                then: {
                                  $round: [
                                    {
                                      $multiply: [
                                        {
                                          $divide: [
                                            '$$report.totalDurationReport',
                                            '$totalDurationOfAllReports',
                                          ],
                                        },
                                        100,
                                      ],
                                    },
                                    2,
                                  ],
                                },
                                else: 0,
                              },
                            },
                          },
                        ],
                      },
                    },
                  },
                },
              ],
            },
          },
        },
      },
    },
    {
      $lookup: {
        from: 'user-reports',
        localField: 'scopeData.reports._id',
        foreignField: 'report',
        pipeline: [
          {
            $match: { deletedAt: null },
          },
          { $sort: { createdAt: -1 } },
          {
            $project: {
              _id: 1,
              report: 1,
              location: 1,
              asset: 1,
              status: 1,
              createdAt: 1,
              updatedAt: 1,
            },
          },
          {
            $lookup: {
              from: 'locations',
              localField: 'location',
              foreignField: '_id',
              pipeline: [
                {
                  $match: { deletedAt: null },
                },
                {
                  $project: { _id: 1, title: 1 },
                },
              ],
              as: 'location',
            },
          },
          {
            $unwind: '$location',
          },
          {
            $lookup: {
              from: 'assets',
              localField: 'asset',
              foreignField: '_id',
              pipeline: [
                {
                  $match: { deletedAt: null },
                },
                {
                  $project: { _id: 1, cableName: 1, fromLocation: 1, toLocation: 1 },
                },
                {
                  $lookup: {
                    from: 'locations',
                    localField: 'fromLocation',
                    foreignField: '_id',
                    pipeline: [
                      {
                        $match: { deletedAt: null },
                      },
                      {
                        $project: { _id: 1, title: 1 },
                      },
                    ],
                    as: 'fromLocation',
                  },
                },
                {
                  $unwind: '$fromLocation',
                },
                {
                  $lookup: {
                    from: 'locations',
                    localField: 'toLocation',
                    foreignField: '_id',
                    pipeline: [
                      {
                        $match: { deletedAt: null },
                      },
                      {
                        $project: { _id: 1, title: 1 },
                      },
                    ],
                    as: 'toLocation',
                  },
                },
                {
                  $unwind: '$toLocation',
                },
              ],
              as: 'asset',
            },
          },
          {
            $unwind: {
              path: '$asset',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $lookup: {
              from: 'user-report-answers',
              localField: '_id',
              foreignField: 'userReport',
              pipeline: [
                {
                  $match: { deletedAt: null },
                },
                {
                  $group: {
                    _id: '$reportQuestion',
                    total: {
                      $sum: 1,
                    },
                  },
                },
              ],
              as: 'totalAnswers',
            },
          },
          {
            $lookup: {
              from: 'report-questions',
              localField: 'totalAnswers._id',
              foreignField: '_id',
              pipeline: [
                {
                  $match: { deletedAt: null },
                },
                {
                  $group: { _id: null, total: { $sum: '$duration' } },
                },
              ],
              as: 'userQuestionAnswerDuration',
            },
          },
          {
            $unwind: {
              path: '$userQuestionAnswerDuration',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $lookup: {
              from: 'reports',
              localField: 'report',
              foreignField: '_id',
              pipeline: [
                {
                  $match: { deletedAt: null },
                },
                {
                  $project: { _id: 1, title: 1, type: 1 },
                },
                {
                  $lookup: {
                    from: 'report-questions',
                    localField: '_id',
                    foreignField: 'report',
                    pipeline: [
                      { $match: { deletedAt: null } },
                      {
                        $group: { _id: null, total: { $sum: '$duration' } },
                      },
                    ],
                    as: 'durationReport',
                  },
                },
                {
                  $unwind: '$durationReport',
                },
              ],
              as: 'report',
            },
          },
          {
            $unwind: '$report',
          },
          {
            $addFields: {
              reportProgress: {
                $cond: {
                  if: {
                    $and: [
                      { $gt: ['$userQuestionAnswerDuration.total', 0] },
                      { $gt: ['$report.durationReport.total', 0] },
                    ],
                  },
                  then: {
                    $round: [
                      {
                        $multiply: [
                          {
                            $divide: [
                              '$userQuestionAnswerDuration.total',
                              '$report.durationReport.total',
                            ],
                          },
                          100,
                        ],
                      },
                      2,
                    ],
                  },
                  else: 0,
                },
              },
            },
          },
          {
            $group: {
              _id: {
                locationId: '$location._id',
                location: '$location.title',
                reportId: '$report._id',
                report: '$report.title',
              },
              assets: { $push: '$asset' },
              reportProgress: { $push: '$reportProgress' },
              totalReportProgress: { $sum: '$reportProgress' },
              durationReport: { $first: '$report.durationReport.total' },
              createdAt: { $last: '$createdAt' },
              updatedAt: { $last: '$updatedAt' },
            },
          },
          {
            $addFields: {
              reportComplitionInPrecentage: {
                $cond: {
                  if: { $in: [100, '$reportProgress'] },
                  then: 100,
                  else: '$totalReportProgress',
                },
              },
              reportComplition: {
                $cond: {
                  if: { $in: [100, '$reportProgress'] },
                  then: 1,
                  else: { $round: [{ $divide: ['$totalReportProgress', 100] }, 2] },
                },
              },
            },
          },
        ],
        as: 'userReports',
      },
    },
    {
      $addFields: {
        //scope report calculation
        scopes: {
          $mergeObjects: {
            scopeData: '$scopeData',
          },
        },
        //users report calculation
        rows: {
          $mergeObjects: {
            userReports: '$userReports',
          },
        },
      },
    },
    {
      $addFields: {
        scopes: {
          $mergeObjects: [
            '$scopes',
            {
              scopeData: {
                $map: {
                  input: '$scopes.scopeData',
                  as: 'scopeData',
                  in: {
                    $mergeObjects: [
                      '$$scopeData',
                      {
                        reports: {
                          $map: {
                            input: '$$scopeData.reports',
                            as: 'report',
                            in: {
                              $let: {
                                vars: {
                                  matchedUserReports: {
                                    $filter: {
                                      input: '$rows.userReports',
                                      as: 'userReport',
                                      cond: { $eq: ['$$userReport._id.reportId', '$$report._id'] },
                                    },
                                  },
                                },
                                in: {
                                  $let: {
                                    vars: {
                                      reportComplitionProgress: {
                                        $reduce: {
                                          input: '$$matchedUserReports',
                                          initialValue: 0,
                                          in: { $add: ['$$value', '$$this.reportComplition'] },
                                        },
                                      },
                                    },
                                    in: {
                                      $mergeObjects: [
                                        '$$report',
                                        {
                                          reportComplitionProgress: '$$reportComplitionProgress',
                                          totalReportComplition: {
                                            $cond: {
                                              if: { $gt: ['$$report.totalDurationReport', 0] },
                                              then: {
                                                $round: [
                                                  {
                                                    $multiply: [
                                                      {
                                                        $divide: [
                                                          {
                                                            $multiply: [
                                                              '$$reportComplitionProgress',
                                                              '$$report.durationReport',
                                                            ],
                                                          },
                                                          '$$report.totalDurationReport',
                                                        ],
                                                      },
                                                      100,
                                                    ],
                                                  },
                                                  2,
                                                ],
                                              },
                                              else: 0,
                                            },
                                          },
                                        },
                                      ],
                                    },
                                  },
                                },
                              },
                            },
                          },
                        },
                      },
                    ],
                  },
                },
              },
            },
          ],
        },
      },
    },
    {
      $addFields: {
        totalCompletionPercentage: {
          $arrayElemAt: [
            {
              $map: {
                input: '$scopes.scopeData',
                as: 'scopeData',
                in: {
                  $round: [
                    {
                      $divide: [
                        {
                          $sum: {
                            $map: {
                              input: '$$scopeData.reports',
                              as: 'report',
                              in: {
                                $multiply: [
                                  '$$report.reportProgress',
                                  '$$report.totalReportComplition',
                                ],
                              },
                            },
                          },
                        },
                        100,
                      ],
                    },
                    2,
                  ],
                },
              },
            },
            0,
          ],
        },
      },
    },
    {
      $project: {
        _id: 0,
        scopes: 1,
        rows: 1,
        totalDurationOfAllReports: '$totalDurationOfAllReports',
        totalCompletionPercentage: 1,
      },
    },
  ]);

  return scopeCal;
};

exports.getUserReportDataForReportCalculation = async requestData => {
  let userReportRows = [];
  for (let scope of requestData.scopeData) {
    for (let report of scope.reports) {
      let commonAggregateData = [
        {
          $lookup: {
            from: 'locations',
            localField: 'location',
            foreignField: '_id',
            pipeline: [
              {
                $match: { deletedAt: null },
              },
              {
                $project: { _id: 1, title: 1 },
              },
            ],
            as: 'location',
          },
        },
        {
          $unwind: '$location',
        },
        {
          $lookup: {
            from: 'user-report-answers',
            localField: '_id',
            foreignField: 'userReport',
            pipeline: [
              {
                $match: { deletedAt: null },
              },
              {
                $group: {
                  _id: '$reportQuestion',
                  total: {
                    $sum: 1,
                  },
                },
              },
            ],
            as: 'totalAnswers',
          },
        },
        {
          $lookup: {
            from: 'report-questions',
            localField: 'totalAnswers._id',
            foreignField: '_id',
            pipeline: [
              {
                $match: { deletedAt: null },
              },
              {
                $group: { _id: null, total: { $sum: '$duration' } },
              },
            ],
            as: 'userQuestionAnswerDuration',
          },
        },
        {
          $unwind: {
            path: '$userQuestionAnswerDuration',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'reports',
            localField: 'report',
            foreignField: '_id',
            pipeline: [
              {
                $match: { deletedAt: null },
              },
              {
                $project: { _id: 1, title: 1, type: 1 },
              },
              {
                $lookup: {
                  from: 'report-questions',
                  localField: '_id',
                  foreignField: 'report',
                  pipeline: [
                    { $match: { deletedAt: null } },
                    {
                      $group: { _id: null, total: { $sum: '$duration' } },
                    },
                  ],
                  as: 'durationReport',
                },
              },
              {
                $unwind: '$durationReport',
              },
            ],
            as: 'report',
          },
        },
        {
          $unwind: '$report',
        },
        {
          $addFields: {
            reportProgress: {
              $cond: {
                if: {
                  $and: [
                    { $gt: ['$userQuestionAnswerDuration.total', 0] },
                    { $gt: ['$report.durationReport.total', 0] },
                  ],
                },
                then: {
                  $round: [
                    {
                      $multiply: [
                        {
                          $divide: [
                            '$userQuestionAnswerDuration.total',
                            '$report.durationReport.total',
                          ],
                        },
                        100,
                      ],
                    },
                    2,
                  ],
                },
                else: 0,
              },
            },
          },
        },
        {
          $group: {
            _id: {
              locationId: '$location._id',
              location: '$location.title',
              reportId: '$report._id',
              report: '$report.title',
              reportType: '$report.type',
            },
            reportProgress: { $push: '$reportProgress' },
            totalReportProgress: { $sum: '$reportProgress' },
            durationReport: { $first: '$report.durationReport.total' },
            createdAt: { $last: '$createdAt' },
            updatedAt: { $last: '$updatedAt' },
          },
        },
        {
          $addFields: {
            reportComplitionInPrecentage: {
              $cond: {
                if: { $in: [100, '$reportProgress'] },
                then: 100,
                else: '$totalReportProgress',
              },
            },
            reportComplition: {
              $cond: {
                if: { $in: [100, '$reportProgress'] },
                then: 1,
                else: { $round: [{ $divide: ['$totalReportProgress', 100] }, 2] },
              },
            },
          },
        },
        {
          $project: {
            _id: 1,
            reportProgress: 1,
            totalReportProgress: 1,
            durationReport: 1,
            createdAt: 1,
            updatedAt: 1,
            reportComplitionInPrecentage: 1,
            reportComplition: 1,
          },
        },
      ];

      let commonFileterUserReport = {
        reportProgress: [],
        totalReportProgress: 0,
        durationReport: 0,
        createdAt: null,
        updatedAt: null,
        reportComplitionInPrecentage: 0,
        reportComplition: 0,
      };

      let commonFilterIds = {
        reportId: report._id,
        report: report.title,
        reportType: report.type,
      };
      if (report.type === 'location') {
        for (let location of report.locations) {
          let userReportAggregate = [
            {
              $match: {
                report: toObjectId(report._id),
                location: toObjectId(location._id),
                deletedAt: null,
              },
            },
            ...commonAggregateData,
          ];

          let userReport = await UserReport.aggregate(userReportAggregate);

          let filterUserReport = {
            _id: {
              locationId: location._id,
              location: location.title,
              ...commonFilterIds,
            },
            ...commonFileterUserReport,
          };

          if (userReport.length > 0) {
            filterUserReport = userReport[0];
          }
          userReportRows.push(filterUserReport);
        }
      } else {
        for (let asset of report.assets) {
          let userReportAggregate = [
            {
              $match: {
                report: toObjectId(report._id),
                location: toObjectId(asset._id.locationId),
                deletedAt: null,
              },
            },
            ...commonAggregateData,
          ];

          let userReport = await UserReport.aggregate(userReportAggregate);

          let filterUserReport = {
            _id: {
              locationId: asset._id.locationId,
              location: asset._id.location,
              ...commonFilterIds,
            },
            ...commonFileterUserReport,
          };

          if (userReport.length > 0) {
            filterUserReport = userReport[0];
          }
          userReportRows.push(filterUserReport);
        }
      }
    }
  }

  return userReportRows;
};

exports.calculateReportDataAndAddKeys = (scopeReports, userReportRows) => {
  let totalCompletionPercentage = 0;
  let sumAllReportProgress = 0;
  for (let scope of scopeReports.scopeData) {
    for (let report of scope.reports) {
      let calReportProgressAndComplition = 0;
      for (let userReport of userReportRows) {
        let reportComplitionTotal = 0;
        if (report._id.toString() === userReport._id.reportId.toString()) {
          reportComplitionTotal += userReport.reportComplition;
          report.reportComplitionProgress = reportComplitionTotal;
          if (report.totalDurationReport > 0) {
            let reportComplition =
              ((reportComplitionTotal * report.durationReport) / report.totalDurationReport) * 100;

            report.totalReportComplition = Number(reportComplition.toFixed(2));
            calReportProgressAndComplition = reportComplition * report.reportProgress;
          }
        }
      }
      sumAllReportProgress += calReportProgressAndComplition;
    }
  }

  totalCompletionPercentage = sumAllReportProgress / 100;
  scopeReports.totalCompletionPercentage = Number(totalCompletionPercentage.toFixed(2));

  return scopeReports;
};

/**
 * Get Detailed Progress Report Data
 *
 * @param {Object} filter
 * @param {Date} dprDate
 * @returns {Object} Detailed Progress Report Data
 */
exports.getDetailedProgressReportData = async (filter, dprDate, sortOrder) => {
  const { startDprDate, endDprDate } = commonfunctionsUtils.getStartAndEndDates(dprDate);

  const pipeline = [
    {
      $match: {
        project: filter.project,
        account: filter.account,
        deletedAt: null,
        createdAt: {
          $gte: startDprDate,
          $lt: endDprDate,
        },
      },
    },
    {
      $lookup: {
        from: 'locations',
        localField: 'location',
        foreignField: '_id',
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
          {
            $project: {
              _id: 1,
              title: 1,
            },
          },
        ],
        as: 'location',
      },
    },
    {
      $unwind: '$location',
    },
    {
      $lookup: {
        from: 'reports',
        localField: 'report',
        foreignField: '_id',
        pipeline: [
          {
            $match: {
              deletedAt: null,
              isProgressable: true,
              isPublish: true,
            },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              type: 1,
            },
          },
        ],
        as: 'report',
      },
    },
    {
      $unwind: '$report',
    },
    {
      $lookup: {
        from: 'assets',
        localField: 'asset.asset',
        foreignField: '_id',
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
          {
            $project: {
              _id: 1,
              cableName: 1,
              fromLocation: 1,
              toLocation: 1,
            },
          },
          {
            $lookup: {
              from: 'locations',
              localField: 'fromLocation',
              foreignField: '_id',
              as: 'fromLocation',
              pipeline: [{ $project: { _id: 1, title: 1 } }],
            },
          },
          { $unwind: '$fromLocation' },
          {
            $lookup: {
              from: 'locations',
              localField: 'toLocation',
              foreignField: '_id',
              as: 'toLocation',
              pipeline: [{ $project: { _id: 1, title: 1 } }],
            },
          },
          { $unwind: '$toLocation' },
        ],
        as: 'assets',
      },
    },
    {
      $lookup: {
        from: 'user-report-answers',
        localField: '_id',
        foreignField: 'userReport',
        as: 'user-report-answers',
        pipeline: [
          {
            $group: {
              _id: '$reportQuestion',
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'report-questions',
        localField: 'user-report-answers._id',
        foreignField: '_id',
        pipeline: [
          {
            $match: {
              deletedAt: null,
              isRequired: true,
            },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              sortOrder: 1,
            },
          },
          {
            $sort: {
              sortOrder: 1,
            },
          },
        ],
        as: 'reportQuestions',
      },
    },
    {
      $group: {
        _id: '$location._id',
        locationTitle: {
          $first: '$location.title',
        },
        reports: {
          $addToSet: {
            _id: '$report._id',
            title: '$report.title',
            type: '$report.type',
            assets: {
              $cond: {
                if: {
                  $or: [
                    {
                      $eq: ['$report.type', 'multiple_assets'],
                    },
                    {
                      $eq: ['$report.type', 'asset_per_location'],
                    },
                  ],
                },
                then: '$assets',
                else: [],
              },
            },
            completedTasks: '$reportQuestions',
          },
        },
        createdAt: {
          $min: '$createdAt',
        },
      },
    },
    {
      $project: {
        _id: 1,
        locationTitle: 1,
        reports: {
          $map: {
            input: '$reports',
            as: 'report',
            in: {
              _id: '$$report._id',
              title: '$$report.title',
              type: '$$report.type',
              assets: '$$report.assets',
              completedTasks: '$$report.completedTasks',
            },
          },
        },
        createdAt: 1,
      },
    },
    {
      $sort: {
        createdAt: sortOrder,
      },
    },
  ];

  return await UserReport.aggregate(pipeline);
};

/**
 * Get project tracker data
 *
 * @param {*} req
 * @param {*} projectId
 * @returns
 */
exports.projectTrackerData = async (req, projectId, dprDate = null) => {
  const scopeFilter = {
    project: toObjectId(projectId),
    account: req.userData.account,
    deletedAt: null,
  };

  if (dprDate) {
    scopeFilter.createdAt = { $lte: dprDate };
  }
  const filter = {
    project: toObjectId(projectId),
    account: req.userData.account,
    deletedAt: null,
  };

  if (dprDate) {
    filter.createdAt = { $lte: dprDate };
  }

  // get All scopes of project
  // get All locations of project which is connected with atleast 1 report
  // get All assets of project which is connected with atleast 1 report
  // eslint-disable-next-line no-undef
  let [scopeData, locationData, assetsData] = await Promise.all([
    scopeService.getProjectTrackerContent(scopeFilter),
    locationService.getAllLocationByProjectForProjectTracker(filter),
    assetService.getAssetByProjectForProjectTracker(filter),
  ]);

  scopeData = scopeData.filter(scope => scope.reports.length > 0);

  // When asset is used in report but the lcoation linked in fromLcoation and toLcoation is not used in report
  // Then adding that location object in lcoationData
  // Identify exceptional locations
  let exceptionalLocations = [];
  assetsData.forEach(asset => {
    ['fromLocation', 'toLocation'].forEach(locType => {
      if (
        asset[locType] &&
        !locationData.some(l => l._id.toString() === asset[locType]._id.toString())
      ) {
        exceptionalLocations.push({
          ...asset[locType],
          reports: [],
          assetsReports: asset.reports,
        });
      }
    });
  });

  const filteredLocationData = exceptionalLocations.filter(
    (item, index) =>
      exceptionalLocations.findIndex(
        loc => loc._id.toString() === item._id.toString() && loc.assetsReports.length > 0
      ) === index
  );

  locationData = [...locationData, ...filteredLocationData];

  let totalDurationsOfAllReports = 0;

  // Calculate requiredReport as per lcoation and asset
  scopeData = scopeData.map(item => ({
    ...item,
    reports: item.reports.map(report => {
      const type = report.type;
      let requiredReports = 0;
      let assets = null;
      switch (type) {
        case 'location': {
          const reportRequired = locationData.filter(
            l => l.reports.filter(r => r._id.toString() === report._id.toString()).length > 0
          );

          if (reportRequired.length > 0) {
            requiredReports = reportRequired.length;
          }
          break;
        }

        case 'asset_per_location': {
          // find all location required for this report
          const locationLinkedWithReport = locationData.filter(
            l => l.reports.filter(r => r._id.toString() === report._id.toString()).length > 0
          );

          //find all possible reports based on location and assets combinations.
          let possibleReport = [];
          locationLinkedWithReport.forEach(l => {
            const linkedAssetWithLocation = assetsData.filter(
              a =>
                l._id.toString() === a.fromLocation._id.toString() ||
                l._id.toString() === a.toLocation._id.toString()
            );
            possibleReport.push(...linkedAssetWithLocation);
          });

          // Find all assets that have this report ID in their reports array
          const assetsWithReport = possibleReport.filter(a =>
            a.reports.some(r => r._id.toString() === report._id.toString())
          );
          requiredReports = assetsWithReport.length;
          assets = assetsWithReport;
          break;
        }

        case 'multiple_assets': {
          let reportRequired;
          reportRequired = assetsData.filter(
            a => a.reports.filter(r => r._id.toString() === report._id.toString()).length > 0
          );

          if (reportRequired.length > 0) {
            const possibleReport = [];
            reportRequired.forEach(item => {
              possibleReport.push(`${item._id}-${item.fromLocation._id}`);
              possibleReport.push(`${item._id}-${item.toLocation._id}`);
            });
            // Filter unique reports
            // eslint-disable-next-line no-undef
            const uniqueKey = [...new Set(possibleReport)];
            requiredReports = uniqueKey.length;
            assets = reportRequired;
          }
          break;
        }
      }

      let totalDurationReport = report.reportDurations * requiredReports;

      totalDurationsOfAllReports += totalDurationReport;
      return {
        ...report,
        requiredReports,
        assets,
        totalDurationReport,
      };
    }),
  }));

  scopeData = scopeData.map(item => ({
    ...item,
    reports: item.reports.map(report => ({
      ...report,
      weightage: (report.totalDurationReport / totalDurationsOfAllReports) * 100,
    })),
  }));

  const rowFormat = {
    _id: null,
    locations: null,
    assets: [
      {
        _id: null,
        title: null,
      },
    ],
  };

  const customReportFormat = (report, isRequired = false, createdReports = {}, duration = 0) => {
    const reportDuration = createdReports.totalDuration || duration || 0;
    const completedDuration = createdReports.totalAnsweredDuration || 0;

    let completion = 0;
    if (reportDuration > 0) {
      completion = (completedDuration / reportDuration) * 100;
    }

    return {
      _id: report._id,
      title: report.title,
      type: report.type,
      createdAt: report.createdAt,
      status: createdReports?.status || [],
      reportDuration,
      completedDuration,
      completedDate: createdReports.updatedAt || null,
      completion,
      isRequired: isRequired,
    };
  };

  // eslint-disable-next-line no-undef
  const projectTrackerData = await Promise.all(
    locationData.map(async location => {
      const tempRawFormat = JSON.parse(JSON.stringify(rowFormat));
      tempRawFormat._id = location._id;
      tempRawFormat.locations = location.title;
      tempRawFormat.reportList = [];

      // Find assets that match fromLocation or toLocation of location
      const filterAssets = assetsData.filter(
        asset =>
          asset.fromLocation._id.toString() === location._id.toString() ||
          asset.toLocation._id.toString() === location._id.toString()
      );
      tempRawFormat.assets = filterAssets.map(asset => ({
        _id: asset._id,
        title: asset.cableName,
        fromLocation: asset.fromLocation,
        toLocation: asset.toLocation,
      }));

      // Flatten the scope reports into a single array
      const flatReports = scopeData.flatMap(scope => scope.reports.map(report => ({ ...report })));

      // Create an array of promises to ensure order is maintained
      const reportPromises = flatReports.map(async report => {
        const tempReport = {
          _id: report._id,
          title: report.title,
          type: report.type,
          createdAt: report.createdAt,
          reportList: [],
        };

        if (report.type === 'location') {
          const isReportLinkedWithLocation = location.reports.find(
            r => r._id.toString() === report._id.toString()
          );
          if (isReportLinkedWithLocation !== undefined) {
            const locationTypeFilter = {
              location: location._id,
              asset: [],
              report: { $in: [report._id] },
              account: req.userData.account,
              deletedAt: null,
            };

            if (dprDate) {
              locationTypeFilter.createdAt = { $lte: dprDate };
            }

            const locationTypeReports = await userReportService.getReportsForCompletion(
              locationTypeFilter,
              null,
              null,
              -1
            );

            if (locationTypeReports.length > 0) {
              const locationReport = customReportFormat(report, true, locationTypeReports[0]);
              tempReport.reportList.push(locationReport);
            } else {
              const locationReport = customReportFormat(report, true, {}, report.reportDurations);
              tempReport.reportList.push(locationReport);
            }
          } else {
            const locationReport = customReportFormat(report);
            tempReport.reportList.push(locationReport);
          }
        }
        // For Asset Per Location and Multiple Assets reports
        else if (filterAssets.length !== 0) {
          for (const asset of filterAssets) {
            let isRequiredReport;
            // For Asset Per Location
            if (report.type === 'asset_per_location') {
              // check the locations is requried for this report if report type is asset_per_location
              isRequiredReport = asset.reports.find(r => {
                const islocationLinkedWithReprot = location.reports.find(
                  lr => lr._id.toString() === report._id.toString()
                );
                if (!islocationLinkedWithReprot) return false;

                // check the asset is required for this report
                return r._id.toString() === report._id.toString();
              });
            }
            // For Multiple Assets
            else {
              // check the asset is required for this report
              isRequiredReport = asset.reports.find(
                r => r._id.toString() === report._id.toString()
              );
            }
            if (isRequiredReport !== undefined) {
              // Find reports that match asset (multiple assets, asset per location)
              let assetReportFilter = {
                'asset.asset': asset._id,
                report: { $in: [report._id] },
                account: req.userData.account,
                deletedAt: null,
              };

              if (dprDate) {
                assetReportFilter.createdAt = { $lte: dprDate };
              }

              // Add location filter if report type is asset_per_location
              assetReportFilter = {
                ...assetReportFilter,
                ...(report.type === 'asset_per_location' ? { location: location._id } : {}),
              };

              const assetTypeReports = await userReportService.getReportsForCompletion(
                assetReportFilter,
                null,
                null,
                -1
              );

              if (assetTypeReports.length > 0) {
                // Get the report which have totalAnsweredDuration which have max value
                let getAssetTypeReport = assetTypeReports[0];
                let tad = 0;
                for (const assetTypeReport of assetTypeReports) {
                  if (assetTypeReport.totalAnsweredDuration > tad) {
                    tad = assetTypeReport.totalAnsweredDuration;
                    getAssetTypeReport = assetTypeReport;
                  }
                }
                tempReport.reportList.push(customReportFormat(report, true, getAssetTypeReport));
              } else {
                tempReport.reportList.push(
                  customReportFormat(report, true, {}, report.reportDurations)
                );
              }
            } else {
              tempReport.reportList.push(customReportFormat(report));
            }
          }
        } else tempReport.reportList.push(customReportFormat(report));

        return tempReport;
      });

      // Await all report promises and assign to reportList in correct order
      // eslint-disable-next-line no-undef
      tempRawFormat.reportList = await Promise.all(reportPromises);
      tempRawFormat.longitude = location.longitude;
      tempRawFormat.latitude = location.latitude;
      return tempRawFormat;
    })
  );

  let totalCompletions = 0;

  scopeData = scopeData.map(scope => ({
    ...scope,
    reports: scope.reports.map(report => {
      let totalCompletedReportDuration = 0;
      projectTrackerData.forEach(projectTracker => {
        const reportIndex = projectTracker.reportList.findIndex(
          reportList => reportList._id.toString() === report._id.toString()
        );
        projectTracker.reportList[reportIndex].reportList.forEach(reportList => {
          if (reportList._id.toString() === report._id.toString()) {
            totalCompletedReportDuration += reportList.completedDuration;
          }
        });
      });

      totalCompletions +=
        (totalCompletedReportDuration / report.totalDurationReport) *
          (report.totalDurationReport / totalDurationsOfAllReports) || 0;

      return {
        ...report,
        totalCompletedReportDuration,
        reportCompletion: (totalCompletedReportDuration / report.totalDurationReport) * 100,
      };
    }),
  }));
  return {
    projectTrackerData,
    scopeData,
    totalDurationsOfAllReports,
    totalCompletions: totalCompletions * 100,
  };
};

exports.getProgressSummary = async data => {
  // Calculate scope data
  const scopes = data.scopeData.reduce((acc, scope) => {
    acc[scope.name] = {
      weightage: scope.reports.reduce((sum, report) => sum + (report.weightage || 0), 0),
      reports: scope.reports.reduce((reportsAcc, report) => {
        reportsAcc[report.title] = {
          completion: report.reportCompletion || 0,
          weightage: report.weightage || 0,
        };
        return reportsAcc;
      }, {}),
    };
    return acc;
  }, {});

  // Build location map
  // eslint-disable-next-line no-undef
  const locationMap = new Map();
  data.projectTrackerData.forEach(location => {
    if (!locationMap.has(location.locations)) {
      locationMap.set(location.locations, {
        title: location.locations,
        assets: location.assets.map(asset => asset.title),
        reportList: location.reportList.reduce((acc, report) => {
          acc[report.title] = {
            completion: report.reportList[0].completion || 0,
          };
          return acc;
        }, {}),
      });
    }
  });

  // Helper function to calculate completions for a location group
  const calculateCompletions = (locationGroup, scopes) => {
    const scopeCompletions = Object.keys(scopes).map(scopeName => {
      const scopeReports = scopes[scopeName].reports;
      const locationReports = locationGroup.reportList;
      let totalCompletion = 0;
      let reportCount = 0;

      Object.entries(scopeReports).forEach(([reportTitle]) => {
        if (locationReports[reportTitle]) {
          totalCompletion += locationReports[reportTitle].completion || 0;
          reportCount++;
        }
      });

      return reportCount > 0 ? totalCompletion / reportCount : 0;
    });

    const validCompletions = scopeCompletions.filter(comp => comp > 0);
    const overallCompletion =
      validCompletions.length > 0
        ? validCompletions.reduce((a, b) => a + b, 0) / validCompletions.length
        : 0;

    return { scopeCompletions, overallCompletion };
  };

  // Generate progress data
  const progressData = [];
  let totalCompletions = [];

  locationMap.forEach(locationGroup => {
    if (locationGroup.assets.length === 0) {
      // Handle empty location
      const { scopeCompletions, overallCompletion } = calculateCompletions(locationGroup, scopes);
      progressData.push({
        location: locationGroup.title,
        asset: null,
        scopeCompletions: scopeCompletions.map(completion => parseFloat(completion.toFixed(0))),
        overallCompletion: parseFloat(overallCompletion.toFixed(0)),
      });
      totalCompletions.push(parseInt(overallCompletion));
    } else {
      locationGroup.assets.forEach(asset => {
        const { scopeCompletions, overallCompletion } = calculateCompletions(locationGroup, scopes);
        totalCompletions.push(parseInt(overallCompletion));

        progressData.push({
          location: locationGroup.title,
          asset: asset,
          scopeCompletions: scopeCompletions.map(completion => parseFloat(completion.toFixed(0))),
          overallCompletion: parseFloat(overallCompletion.toFixed(0)),
        });
      });
    }
  });

  // Calculate overall completion
  const overallCompletion = data.totalCompletions
    ? parseFloat(data.totalCompletions.toFixed(0))
    : 0;

  return {
    scopes: Object.keys(scopes),
    progressData,
    totalCompletion: overallCompletion,
    summary: {
      totalLocations: locationMap.size,
      totalAssets: progressData.filter(item => item.asset !== null).length,
      averageCompletion:
        totalCompletions.length > 0
          ? parseFloat(
              (
                totalCompletions.reduce((sum, completion) => sum + completion, 0) /
                totalCompletions.length
              ).toFixed(0)
            )
          : 0,
    },
  };
};
